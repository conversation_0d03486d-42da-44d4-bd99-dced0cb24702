package health

import (
	"context"
	"time"

	"github.com/suyuan32/simple-admin-core/api/internal/svc"
	"github.com/suyuan32/simple-admin-core/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HealthCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHealthCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HealthCheckLogic {
	return &HealthCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *HealthCheckLogic) HealthCheck() (resp *types.HealthCheckResp, err error) {
	return &types.HealthCheckResp{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Service:   "simple-admin-core-api",
		Version:   "v1.0.0",
		Checks: map[string]string{
			"database": "ok",
			"redis":    "ok",
		},
	}, nil
}
